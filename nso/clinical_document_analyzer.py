#!/usr/bin/env python3
"""
Clinical Document Analysis Assistant

This script extracts and structures clinical data from PDF documents containing
multiple tables with clinical findings, judgements, and recommended actions.
"""

import json
import re
import logging
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

try:
    import PyPDF2
    import pdfplumber
except ImportError:
    print("Required packages not installed. Please run:")
    print("pip install PyPDF2 pdfplumber")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ClinicalRecord:
    """Data structure for a single clinical record extracted from a table."""
    category: str
    findings: str
    clinical_judgement: str
    actions: str
    health_education: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, omitting None values."""
        result = asdict(self)
        return {k: v for k, v in result.items() if v is not None}

class ClinicalDocumentAnalyzer:
    """Main class for analyzing clinical PDF documents."""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = Path(pdf_path)
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    
    def extract_text_with_pdfplumber(self) -> str:
        """Extract text from PDF using pdfplumber for better table detection."""
        full_text = ""
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    logger.info(f"Processing page {page_num}")
                    text = page.extract_text()
                    if text:
                        full_text += f"\n--- PAGE {page_num} ---\n{text}\n"
        except Exception as e:
            logger.error(f"Error extracting text with pdfplumber: {e}")
            raise
        return full_text
    
    def clean_ocr_artifacts(self, text: str) -> str:
        """Clean common OCR artifacts and formatting issues."""
        # Common OCR corrections
        corrections = {
            r'HXAMINATIONCLINICAL\s+jllDGEMENT': 'EXAMINATION / CLINICAL JUDGEMENT',
            r'jllDGEMENT': 'JUDGEMENT',
            r'HXAMINATION': 'EXAMINATION',
            r'([a-z])([A-Z])': r'\1 \2',  # Add space between lowercase and uppercase
            r'\s+': ' ',  # Normalize whitespace
            r'([.!?])\s*([A-Z])': r'\1 \2',  # Ensure space after sentence endings
        }
        
        cleaned_text = text
        for pattern, replacement in corrections.items():
            cleaned_text = re.sub(pattern, replacement, cleaned_text)
        
        return cleaned_text.strip()
    
    def identify_table_sections(self, text: str) -> List[str]:
        """Identify and segment individual table sections from the document."""
        sections = []

        # Look for table structures with COMPLAINTS, FINDINGS, CLINICAL JUDGEMENT, ACTION columns
        table_pattern = r'COMPLAINTS\s+FINDINGS\s+ON\s+CLINICAL\s+EXAMINATION\s+JUDGEMENT\s+ACTION'

        # Split by table headers or major section breaks
        parts = re.split(table_pattern, text, flags=re.IGNORECASE)

        if len(parts) > 1:
            # We found table structures
            for i, part in enumerate(parts[1:], 1):  # Skip the first part (before first table)
                # Look for individual rows in the table
                rows = self.extract_table_rows(part)
                sections.extend(rows)
        else:
            # Fallback: look for other section patterns
            sections = self.extract_sections_by_patterns(text)

        return sections

    def extract_table_rows(self, table_text: str) -> List[str]:
        """Extract individual rows from a table section."""
        rows = []

        # Split by Roman numerals or numbered items (I., II., III., 1., 2., 3., etc.)
        row_patterns = [
            r'\n\s*(?:I{1,3}|IV|V|VI{0,3}|IX|X)\.\s+',  # Roman numerals
            r'\n\s*\d+\.\s+',  # Arabic numerals
            r'\n\s*[a-z]\.\s+',  # Lowercase letters
        ]

        current_text = table_text
        for pattern in row_patterns:
            parts = re.split(pattern, current_text, flags=re.IGNORECASE)
            if len(parts) > 1:
                for part in parts[1:]:  # Skip first empty part
                    if part.strip():
                        rows.append(part.strip())
                break

        # If no clear row structure, try to split by major content blocks
        if not rows:
            # Look for content blocks separated by multiple newlines
            blocks = re.split(r'\n\s*\n\s*', table_text)
            for block in blocks:
                if len(block.strip()) > 50:  # Only consider substantial blocks
                    rows.append(block.strip())

        return rows

    def extract_sections_by_patterns(self, text: str) -> List[str]:
        """Extract sections using pattern-based approach."""
        sections = []

        # Look for section headers and content blocks
        section_patterns = [
            r'(?i)\d+\.\d+\.?\s+[A-Z][^.]*(?:COMPLAINT|CONDITION|PROBLEM)',
            r'(?i)HISTORY\s+EXAMINATION',
            r'(?i)FINDINGS\s+ON\s+CLINICAL',
        ]

        lines = text.split('\n')
        current_section = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if this line starts a new major section
            is_new_section = any(re.search(pattern, line) for pattern in section_patterns)

            if is_new_section and current_section and len('\n'.join(current_section)) > 100:
                sections.append('\n'.join(current_section))
                current_section = [line]
            else:
                current_section.append(line)

        # Add the last section
        if current_section and len('\n'.join(current_section)) > 100:
            sections.append('\n'.join(current_section))

        return sections
    
    def extract_field_content(self, section_text: str, field_patterns: List[str]) -> str:
        """Extract content for a specific field using multiple patterns."""
        content = ""
        
        for pattern in field_patterns:
            match = re.search(pattern, section_text, re.IGNORECASE | re.DOTALL)
            if match:
                content = match.group(1).strip()
                break
        
        return self.clean_ocr_artifacts(content) if content else ""
    
    def parse_clinical_record(self, section_text: str) -> Optional[ClinicalRecord]:
        """Parse a section of text into a structured clinical record."""
        if not section_text.strip() or len(section_text.strip()) < 50:
            return None

        # Try to extract category from section headers or first meaningful line
        category = self.extract_category(section_text)
        if not category:
            return None

        # Define patterns for each field based on the document structure
        field_patterns = {
            'findings': [
                # Look for findings in table format or bullet points
                r'(?i)(?:findings?|examination|signs?|symptoms?)[:\s]*(.+?)(?=clinical\s+judgement?|actions?|treatment|health\s+education|\n\s*\n|$)',
                r'(?i)(?:•|\*|\-|\d+\.)\s*(.+?)(?=clinical\s+judgement?|actions?|treatment|$)',
                # Extract content between specific markers
                r'(?s)(?:examination|findings?)[:\s]*(.+?)(?=(?:clinical\s+judgement?|actions?|treatment|health\s+education))',
            ],
            'clinical_judgement': [
                r'(?i)(?:clinical\s+judgement?|assessment|diagnosis|condition)[:\s]*(.+?)(?=actions?|treatment|interventions?|health\s+education|\n\s*\n|$)',
                r'(?i)(?:suspect|moderate|severe|mild)\s+condition[:\s]*(.+?)(?=actions?|treatment|$)',
                r'(?s)(?:clinical\s+judgement?|assessment)[:\s]*(.+?)(?=(?:actions?|treatment|interventions?))',
            ],
            'actions': [
                r'(?i)(?:actions?|treatment|interventions?|management)[:\s]*(.+?)(?=health\s+education|\n\s*\n|$)',
                r'(?i)(?:give|refer|advise|apply|continue)[:\s]*(.+?)(?=health\s+education|$)',
                r'(?s)(?:actions?|treatment)[:\s]*(.+?)(?=(?:health\s+education|$))',
            ],
            'health_education': [
                r'(?i)(?:health\s+education|patient\s+education|advice)[:\s]*(.+?)$',
                r'(?i)(?:encourage|advise|counsel|remind)[:\s]*(.+?)$',
                r'(?s)(?:health\s+education)[:\s]*(.+?)$',
            ],
        }

        # Extract each field
        extracted_data = {'category': category}
        for field, patterns in field_patterns.items():
            content = self.extract_field_content(section_text, patterns)
            if content:
                extracted_data[field] = content

        # Create a record if we have meaningful content
        if len(extracted_data) >= 2:  # At least category and one other field
            return ClinicalRecord(
                category=extracted_data.get('category', ''),
                findings=extracted_data.get('findings', ''),
                clinical_judgement=extracted_data.get('clinical_judgement', ''),
                actions=extracted_data.get('actions', ''),
                health_education=extracted_data.get('health_education')
            )

        return None

    def extract_category(self, section_text: str) -> str:
        """Extract category/complaint title from section text."""
        lines = section_text.split('\n')

        # Look for numbered sections or clear headings
        category_patterns = [
            r'(?i)^\s*\d+\.\d*\s*(.+?)(?:\s|$)',  # 1.1, 2.3, etc.
            r'(?i)^\s*[IVX]+\.\s*(.+?)(?:\s|$)',  # Roman numerals
            r'(?i)^\s*([A-Z][^.]*(?:COMPLAINT|CONDITION|PROBLEM|EMERGENCY)[^.]*)',
            r'(?i)^\s*([A-Z][A-Z\s]{10,50})',  # All caps titles
            r'(?i)complaint[:\s]*(.+?)(?:\n|$)',
        ]

        for line in lines[:5]:  # Check first few lines
            line = line.strip()
            if not line:
                continue

            for pattern in category_patterns:
                match = re.search(pattern, line)
                if match:
                    category = match.group(1).strip()
                    if len(category) > 5 and len(category) < 100:
                        return category

        # Fallback: use first substantial line
        for line in lines[:3]:
            line = line.strip()
            if len(line) > 10 and len(line) < 100:
                return line

        return "Unknown Category"
    
    def analyze_document(self) -> List[Dict[str, Any]]:
        """Main method to analyze the clinical document and extract structured data."""
        logger.info(f"Starting analysis of {self.pdf_path}")
        
        # Extract text from PDF
        raw_text = self.extract_text_with_pdfplumber()
        
        # Clean OCR artifacts
        cleaned_text = self.clean_ocr_artifacts(raw_text)
        
        # Identify table sections
        sections = self.identify_table_sections(cleaned_text)
        logger.info(f"Found {len(sections)} potential sections")
        
        # Parse each section into clinical records
        clinical_records = []
        for i, section in enumerate(sections, 1):
            logger.info(f"Processing section {i}")
            record = self.parse_clinical_record(section)
            if record:
                clinical_records.append(record.to_dict())
                logger.info(f"Extracted record: {record.category}")
        
        logger.info(f"Successfully extracted {len(clinical_records)} clinical records")
        return clinical_records

def main():
    """Main function to run the clinical document analyzer."""
    pdf_path = "junior_community.pdf"
    
    try:
        analyzer = ClinicalDocumentAnalyzer(pdf_path)
        results = analyzer.analyze_document()
        
        # Save results to JSON file
        output_file = "clinical_analysis_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\nAnalysis complete! Results saved to {output_file}")
        print(f"Extracted {len(results)} clinical records")
        
        # Display summary
        if results:
            print("\nSummary of extracted records:")
            for i, record in enumerate(results, 1):
                print(f"{i}. {record.get('category', 'Unknown Category')}")
    
    except Exception as e:
        logger.error(f"Error during analysis: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
