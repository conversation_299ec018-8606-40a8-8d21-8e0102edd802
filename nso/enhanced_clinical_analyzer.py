#!/usr/bin/env python3
"""
Enhanced Clinical Document Analysis Assistant

This script is specifically designed to extract clinical data from the junior_community.pdf
document which contains structured tables with clinical findings, judgements, and actions.
"""

import json
import re
import logging
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

try:
    import pdfplumber
except ImportError:
    print("Required packages not installed. Please run:")
    print("pip install pdfplumber")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ClinicalRecord:
    """Data structure for a single clinical record extracted from a table."""
    category: str
    findings: str
    clinical_judgement: str
    actions: str
    health_education: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, omitting None values."""
        result = asdict(self)
        return {k: v for k, v in result.items() if v is not None}

class EnhancedClinicalAnalyzer:
    """Enhanced analyzer specifically for the junior community clinical document."""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = Path(pdf_path)
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    
    def extract_text_with_structure(self) -> str:
        """Extract text while preserving table structure."""
        full_text = ""
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    logger.info(f"Processing page {page_num}")
                    
                    # Try to extract tables first
                    tables = page.extract_tables()
                    if tables:
                        for table in tables:
                            table_text = self.format_table_as_text(table)
                            full_text += f"\n--- TABLE ON PAGE {page_num} ---\n{table_text}\n"
                    
                    # Also extract regular text
                    text = page.extract_text()
                    if text:
                        full_text += f"\n--- PAGE {page_num} TEXT ---\n{text}\n"
                        
        except Exception as e:
            logger.error(f"Error extracting text: {e}")
            raise
        return full_text
    
    def format_table_as_text(self, table: List[List[str]]) -> str:
        """Format extracted table data as structured text."""
        if not table:
            return ""
        
        formatted_lines = []
        for row in table:
            if row and any(cell for cell in row if cell):  # Skip empty rows
                # Clean and join cells
                cleaned_row = [str(cell).strip() if cell else "" for cell in row]
                formatted_lines.append(" | ".join(cleaned_row))
        
        return "\n".join(formatted_lines)
    
    def identify_clinical_sections(self, text: str) -> List[Dict[str, str]]:
        """Identify clinical sections with their components."""
        sections = []
        
        # Look for table structures with clinical data
        table_pattern = r'COMPLAINTS\s*\|\s*FINDINGS\s+ON\s+EXAMINATION\s*\|\s*CLINICAL\s+JUDGEMENT\s*\|\s*ACTION'
        
        # Split text into chunks around table headers
        parts = re.split(table_pattern, text, flags=re.IGNORECASE)
        
        for part in parts[1:]:  # Skip the first part (before first table)
            rows = self.extract_clinical_rows(part)
            sections.extend(rows)
        
        # Also look for non-table structured content
        additional_sections = self.extract_structured_content(text)
        sections.extend(additional_sections)
        
        return sections
    
    def extract_clinical_rows(self, table_text: str) -> List[Dict[str, str]]:
        """Extract individual clinical records from table text."""
        records = []
        
        # Split by row indicators (Roman numerals, numbers, etc.)
        row_patterns = [
            r'\n\s*(?:I{1,3}|IV|V|VI{0,3}|IX|X)\.\s*',  # Roman numerals
            r'\n\s*\d+\.\s*',  # Arabic numerals
            r'\n\s*[a-z]\)\s*',  # Letters with parentheses
        ]
        
        for pattern in row_patterns:
            parts = re.split(pattern, table_text, flags=re.IGNORECASE)
            if len(parts) > 1:
                for part in parts[1:]:
                    record = self.parse_table_row(part.strip())
                    if record:
                        records.append(record)
                break
        
        return records
    
    def parse_table_row(self, row_text: str) -> Optional[Dict[str, str]]:
        """Parse a single table row into clinical record components."""
        if not row_text or len(row_text) < 20:
            return None
        
        # Split by pipe characters if present (table format)
        if '|' in row_text:
            parts = [part.strip() for part in row_text.split('|')]
            if len(parts) >= 3:
                return {
                    'category': parts[0] if parts[0] else 'Unknown',
                    'findings': parts[1] if len(parts) > 1 else '',
                    'clinical_judgement': parts[2] if len(parts) > 2 else '',
                    'actions': parts[3] if len(parts) > 3 else '',
                    'health_education': parts[4] if len(parts) > 4 else None
                }
        
        # Try to parse unstructured text
        return self.parse_unstructured_clinical_text(row_text)
    
    def parse_unstructured_clinical_text(self, text: str) -> Optional[Dict[str, str]]:
        """Parse unstructured clinical text into components."""
        # Extract category from first line or sentence
        lines = text.split('\n')
        category = lines[0].strip() if lines else "Unknown"
        
        # Look for key clinical terms and extract surrounding context
        findings_patterns = [
            r'(?i)(?:examination|findings?|signs?|symptoms?)[:\s]*([^.]*(?:\.|$))',
            r'(?i)(?:temp|temperature|fever|breathing|chest)[:\s]*([^.]*(?:\.|$))',
        ]
        
        judgement_patterns = [
            r'(?i)(?:condition|suspect|diagnosis|assessment)[:\s]*([^.]*(?:\.|$))',
            r'(?i)(?:severe|moderate|mild)\s+condition[:\s]*([^.]*(?:\.|$))',
        ]
        
        action_patterns = [
            r'(?i)(?:give|refer|treat|apply|continue)[:\s]*([^.]*(?:\.|$))',
            r'(?i)(?:action|treatment|management)[:\s]*([^.]*(?:\.|$))',
        ]
        
        education_patterns = [
            r'(?i)(?:health\s+education|advice|counsel|encourage)[:\s]*([^.]*(?:\.|$))',
        ]
        
        findings = self.extract_with_patterns(text, findings_patterns)
        judgement = self.extract_with_patterns(text, judgement_patterns)
        actions = self.extract_with_patterns(text, action_patterns)
        education = self.extract_with_patterns(text, education_patterns)
        
        if any([findings, judgement, actions]):
            return {
                'category': category[:100],  # Limit length
                'findings': findings,
                'clinical_judgement': judgement,
                'actions': actions,
                'health_education': education if education else None
            }
        
        return None
    
    def extract_with_patterns(self, text: str, patterns: List[str]) -> str:
        """Extract text using multiple patterns."""
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip()
        return ""
    
    def extract_structured_content(self, text: str) -> List[Dict[str, str]]:
        """Extract structured content from non-table sections."""
        records = []
        
        # Look for numbered sections with clinical content
        section_pattern = r'(\d+\.\d+\.?\s+[A-Z][^.]*(?:COMPLAINT|CONDITION|PROBLEM|EMERGENCY)[^.]*)'
        sections = re.split(section_pattern, text, flags=re.IGNORECASE)
        
        for i in range(1, len(sections), 2):  # Every other element is a header
            if i + 1 < len(sections):
                header = sections[i].strip()
                content = sections[i + 1].strip()
                
                if len(content) > 50:  # Only process substantial content
                    record = self.parse_unstructured_clinical_text(f"{header}\n{content}")
                    if record:
                        records.append(record)
        
        return records
    
    def analyze_document(self) -> List[Dict[str, Any]]:
        """Main method to analyze the clinical document."""
        logger.info(f"Starting enhanced analysis of {self.pdf_path}")
        
        # Extract text with structure preservation
        raw_text = self.extract_text_with_structure()
        
        # Identify clinical sections
        sections = self.identify_clinical_sections(raw_text)
        logger.info(f"Found {len(sections)} potential clinical records")
        
        # Convert to ClinicalRecord objects and filter
        clinical_records = []
        for section in sections:
            if section and len(section.get('category', '')) > 3:
                record = ClinicalRecord(
                    category=section.get('category', ''),
                    findings=section.get('findings', ''),
                    clinical_judgement=section.get('clinical_judgement', ''),
                    actions=section.get('actions', ''),
                    health_education=section.get('health_education')
                )
                clinical_records.append(record.to_dict())
                logger.info(f"Extracted record: {record.category[:50]}...")
        
        logger.info(f"Successfully extracted {len(clinical_records)} clinical records")
        return clinical_records

def main():
    """Main function to run the enhanced clinical document analyzer."""
    pdf_path = "junior_community.pdf"
    
    try:
        analyzer = EnhancedClinicalAnalyzer(pdf_path)
        results = analyzer.analyze_document()
        
        # Save results to JSON file
        output_file = "enhanced_clinical_analysis_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\nEnhanced analysis complete! Results saved to {output_file}")
        print(f"Extracted {len(results)} clinical records")
        
        # Display summary
        if results:
            print("\nSummary of extracted records:")
            for i, record in enumerate(results[:10], 1):  # Show first 10
                print(f"{i}. {record.get('category', 'Unknown Category')[:60]}...")
            
            if len(results) > 10:
                print(f"... and {len(results) - 10} more records")
    
    except Exception as e:
        logger.error(f"Error during analysis: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
