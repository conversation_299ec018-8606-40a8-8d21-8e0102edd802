<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Enhanced Clinical Records - Age-Based Classification</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-bar {
            background: #ecf0f1;
            padding: 20px;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
            min-width: 150px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .search-container {
            margin-bottom: 20px;
        }
        
        .search-box {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            transition: border-color 0.3s;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .age-groups {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .age-group-btn {
            padding: 12px 20px;
            background: white;
            border: 2px solid #3498db;
            color: #3498db;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .age-group-btn:hover,
        .age-group-btn.active {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .severity-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .severity-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .severity-btn.mild { background: #2ecc71; color: white; }
        .severity-btn.moderate { background: #f39c12; color: white; }
        .severity-btn.severe { background: #e74c3c; color: white; }
        .severity-btn.critical { background: #8e44ad; color: white; }
        
        .severity-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .content {
            padding: 30px;
        }
        
        .age-group-section {
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .age-group-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }
        
        .records-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .record-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
        }
        
        .record-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .record-header {
            border-bottom: 2px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .record-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .record-meta {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .meta-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .meta-tag.system {
            background: #e8f4fd;
            color: #2980b9;
        }
        
        .meta-tag.severity {
            color: white;
        }
        
        .meta-tag.severity.mild { background: #27ae60; }
        .meta-tag.severity.moderate { background: #f39c12; }
        .meta-tag.severity.severe { background: #e74c3c; }
        .meta-tag.severity.critical { background: #8e44ad; }
        
        .record-section {
            margin-bottom: 15px;
        }
        
        .section-title {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 8px;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .section-content {
            color: #555;
            line-height: 1.6;
        }
        
        .section-content ul {
            margin-left: 20px;
        }
        
        .section-content li {
            margin-bottom: 5px;
        }
        
        .medication-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 8px;
            border-left: 4px solid #3498db;
        }
        
        .medication-name {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .expandable {
            cursor: pointer;
            user-select: none;
        }
        
        .expandable:hover {
            background: #f8f9fa;
        }
        
        .collapsed {
            display: none;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .stats-bar {
                flex-direction: column;
                gap: 10px;
            }
            
            .age-groups {
                justify-content: center;
            }
            
            .records-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Medical Enhanced Clinical Records</h1>
            <p>Comprehensive Age-Based Clinical Classification System</p>
        </div>
        
        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number" id="totalRecords">0</div>
                <div class="stat-label">Total Records</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="ageGroups">0</div>
                <div class="stat-label">Age Groups</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="medicalSystems">0</div>
                <div class="stat-label">Medical Systems</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="medications">0</div>
                <div class="stat-label">Medications</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="search-container">
                <input type="text" id="searchBox" class="search-box" 
                       placeholder="Search by condition, symptoms, medications, or treatment...">
            </div>
            
            <div class="age-groups" id="ageGroupFilters">
                <!-- Age group buttons will be populated here -->
            </div>
            
            <div class="severity-filters">
                <button class="severity-btn mild" data-severity="mild">Mild</button>
                <button class="severity-btn moderate" data-severity="moderate">Moderate</button>
                <button class="severity-btn severe" data-severity="severe">Severe</button>
                <button class="severity-btn critical" data-severity="critical">Critical</button>
                <button class="severity-btn" data-severity="all" style="background: #95a5a6;">All Severities</button>
            </div>
        </div>
        
        <div class="content" id="recordsContainer">
            <!-- Records will be loaded here -->
        </div>
    </div>

    <script>
        let allRecords = {};
        let filteredRecords = {};
        let currentAgeGroup = 'all';
        let currentSeverity = 'all';

        // Load the clinical records
        async function loadRecords() {
            try {
                const response = await fetch('improved_clinical_records.json');
                allRecords = await response.json();
                filteredRecords = { ...allRecords };
                
                updateStats();
                createAgeGroupFilters();
                displayRecords();
            } catch (error) {
                console.error('Error loading records:', error);
                document.getElementById('recordsContainer').innerHTML = 
                    '<div class="no-results">Error loading clinical records. Please ensure the JSON file is available.</div>';
            }
        }

        function updateStats() {
            const totalRecords = Object.values(allRecords).reduce((sum, group) => sum + group.length, 0);
            const ageGroups = Object.keys(allRecords).length;
            
            // Count unique medical systems
            const systems = new Set();
            const medications = new Set();
            
            Object.values(allRecords).forEach(group => {
                group.forEach(record => {
                    if (record.medical_system) systems.add(record.medical_system);
                    if (record.medications) {
                        record.medications.forEach(med => medications.add(med.name));
                    }
                });
            });
            
            document.getElementById('totalRecords').textContent = totalRecords;
            document.getElementById('ageGroups').textContent = ageGroups;
            document.getElementById('medicalSystems').textContent = systems.size;
            document.getElementById('medications').textContent = medications.size;
        }

        function createAgeGroupFilters() {
            const container = document.getElementById('ageGroupFilters');
            
            // Add "All Ages" button
            const allBtn = document.createElement('button');
            allBtn.className = 'age-group-btn active';
            allBtn.textContent = 'All Age Groups';
            allBtn.onclick = () => filterByAgeGroup('all');
            container.appendChild(allBtn);
            
            // Add buttons for each age group
            Object.keys(allRecords).forEach(ageGroup => {
                const btn = document.createElement('button');
                btn.className = 'age-group-btn';
                btn.textContent = ageGroup;
                btn.onclick = () => filterByAgeGroup(ageGroup);
                container.appendChild(btn);
            });
        }

        function filterByAgeGroup(ageGroup) {
            currentAgeGroup = ageGroup;
            
            // Update active button
            document.querySelectorAll('.age-group-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            applyFilters();
        }

        function filterBySeverity(severity) {
            currentSeverity = severity;
            
            // Update active button
            document.querySelectorAll('.severity-btn').forEach(btn => {
                btn.style.opacity = '0.6';
            });
            event.target.style.opacity = '1';
            
            applyFilters();
        }

        function applyFilters() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            
            filteredRecords = {};
            
            Object.entries(allRecords).forEach(([ageGroup, records]) => {
                // Filter by age group
                if (currentAgeGroup !== 'all' && ageGroup !== currentAgeGroup) {
                    return;
                }
                
                // Filter records within the age group
                const filtered = records.filter(record => {
                    // Severity filter
                    if (currentSeverity !== 'all') {
                        const recordSeverity = record.severity ? record.severity.toLowerCase() : '';
                        if (!recordSeverity.includes(currentSeverity)) {
                            return false;
                        }
                    }
                    
                    // Search filter
                    if (searchTerm) {
                        const searchableText = [
                            record.category,
                            record.chief_complaint,
                            record.primary_diagnosis,
                            JSON.stringify(record.clinical_findings),
                            JSON.stringify(record.medications),
                            JSON.stringify(record.immediate_actions)
                        ].join(' ').toLowerCase();
                        
                        if (!searchableText.includes(searchTerm)) {
                            return false;
                        }
                    }
                    
                    return true;
                });
                
                if (filtered.length > 0) {
                    filteredRecords[ageGroup] = filtered;
                }
            });
            
            displayRecords();
        }

        function displayRecords() {
            const container = document.getElementById('recordsContainer');
            
            if (Object.keys(filteredRecords).length === 0) {
                container.innerHTML = '<div class="no-results">No records found matching your criteria.</div>';
                return;
            }
            
            let html = '';
            
            Object.entries(filteredRecords).forEach(([ageGroup, records]) => {
                html += `
                    <div class="age-group-section">
                        <div class="age-group-header expandable" onclick="toggleSection('${ageGroup}')">
                            ${ageGroup} (${records.length} records)
                        </div>
                        <div class="records-grid" id="section-${ageGroup}">
                `;
                
                records.forEach(record => {
                    html += createRecordCard(record);
                });
                
                html += `
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function createRecordCard(record) {
            const severityClass = record.severity ? record.severity.toLowerCase().replace(/\s+/g, '-') : 'unknown';
            
            return `
                <div class="record-card">
                    <div class="record-header">
                        <div class="record-title">${record.category}</div>
                        <div class="record-meta">
                            <span class="meta-tag system">${record.medical_system || 'General'}</span>
                            <span class="meta-tag severity ${severityClass}">${record.severity || 'Unknown'}</span>
                        </div>
                    </div>
                    
                    <div class="record-section">
                        <div class="section-title">Chief Complaint</div>
                        <div class="section-content">${record.chief_complaint || 'Not specified'}</div>
                    </div>
                    
                    <div class="record-section">
                        <div class="section-title">Clinical Findings</div>
                        <div class="section-content">
                            ${Array.isArray(record.clinical_findings) ? 
                                '<ul>' + record.clinical_findings.map(finding => `<li>${finding}</li>`).join('') + '</ul>' :
                                record.clinical_findings || 'Not specified'}
                        </div>
                    </div>
                    
                    <div class="record-section">
                        <div class="section-title">Primary Diagnosis</div>
                        <div class="section-content">${record.primary_diagnosis || 'Not specified'}</div>
                    </div>
                    
                    ${record.medications && record.medications.length > 0 ? `
                    <div class="record-section">
                        <div class="section-title">Medications</div>
                        <div class="section-content">
                            ${record.medications.map(med => `
                                <div class="medication-item">
                                    <div class="medication-name">${med.name}</div>
                                    ${med.dose ? `<div>Dose: ${med.dose}</div>` : ''}
                                    ${med.duration ? `<div>Duration: ${med.duration}</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}
                    
                    ${record.referral_criteria && record.referral_criteria.length > 0 ? `
                    <div class="record-section">
                        <div class="section-title">Referral Criteria</div>
                        <div class="section-content">
                            <ul>${record.referral_criteria.map(criteria => `<li>${criteria}</li>`).join('')}</ul>
                        </div>
                    </div>
                    ` : ''}
                    
                    ${record.health_education && record.health_education.length > 0 ? `
                    <div class="record-section">
                        <div class="section-title">Health Education</div>
                        <div class="section-content">
                            <ul>${record.health_education.map(education => `<li>${education}</li>`).join('')}</ul>
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
        }

        function toggleSection(ageGroup) {
            const section = document.getElementById(`section-${ageGroup}`);
            section.classList.toggle('collapsed');
        }

        // Event listeners
        document.getElementById('searchBox').addEventListener('input', applyFilters);
        
        document.querySelectorAll('.severity-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                filterBySeverity(this.dataset.severity);
            });
        });

        // Load records when page loads
        loadRecords();
    </script>
</body>
</html>
