#!/usr/bin/env python3
"""
Medical Enhanced Clinical Document Analyzer

This script provides a comprehensive medical analysis with proper age-based groupings,
enhanced clinical details, and medical professional-grade categorization.
"""

import json
import re
import logging
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AgeGroup(Enum):
    """Age group classifications for medical conditions"""
    NEONATE = "0-28 days (Neonate)"
    INFANT_EARLY = "1-6 months (Early Infant)"
    INFANT_LATE = "6-12 months (Late Infant)"
    TODDLER = "1-3 years (<PERSON><PERSON>)"
    PRESCHOOL = "3-5 years (Preschool)"
    SCHOOL_AGE = "5-12 years (School Age)"
    ADOLESCENT = "12-18 years (Adolescent)"
    YOUNG_ADULT = "18-35 years (Young Adult)"
    ADULT = "35-65 years (Adult)"
    ELDERLY = "65+ years (Elderly)"
    ALL_AGES = "All Ages"
    MATERNAL = "Maternal/Reproductive Health"

class Severity(Enum):
    """Clinical severity classifications"""
    MILD = "Mild"
    MODERATE = "Moderate"
    SEVERE = "Severe"
    CRITICAL = "Critical/Emergency"
    UNKNOWN = "Unknown"

class MedicalSystem(Enum):
    """Medical system classifications"""
    DERMATOLOGICAL = "Dermatological"
    RESPIRATORY = "Respiratory"
    CARDIOVASCULAR = "Cardiovascular"
    GASTROINTESTINAL = "Gastrointestinal"
    NEUROLOGICAL = "Neurological"
    MUSCULOSKELETAL = "Musculoskeletal"
    GENITOURINARY = "Genitourinary"
    ENDOCRINE = "Endocrine"
    HEMATOLOGICAL = "Hematological"
    INFECTIOUS = "Infectious Disease"
    OPHTHALMOLOGICAL = "Ophthalmological"
    ENT = "Ear, Nose, Throat"
    PSYCHIATRIC = "Psychiatric/Mental Health"
    OBSTETRIC = "Obstetric/Gynecological"
    PEDIATRIC = "Pediatric Specific"
    EMERGENCY = "Emergency Medicine"
    PREVENTIVE = "Preventive Care"

@dataclass
class EnhancedClinicalRecord:
    """Enhanced data structure for clinical records with medical classifications"""
    # Basic Information
    record_id: str
    category: str
    
    # Medical Classifications
    age_group: AgeGroup
    medical_system: MedicalSystem
    severity: Severity
    
    # Clinical Content
    chief_complaint: str
    clinical_findings: str
    differential_diagnosis: List[str]
    primary_diagnosis: str
    clinical_assessment: str
    
    # Treatment and Management
    immediate_actions: List[str]
    medications: List[Dict[str, str]]  # name, dose, frequency, duration
    procedures: List[str]
    referral_criteria: List[str]
    
    # Follow-up and Education
    follow_up_schedule: str
    health_education: List[str]
    prevention_measures: List[str]
    
    # Additional Medical Details
    contraindications: List[str]
    complications: List[str]
    prognosis: str
    
    # Metadata
    evidence_level: str
    last_updated: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with enum values as strings"""
        result = asdict(self)
        result['age_group'] = self.age_group.value
        result['medical_system'] = self.medical_system.value
        result['severity'] = self.severity.value
        return {k: v for k, v in result.items() if v}

class MedicalEnhancedAnalyzer:
    """Medical-grade analyzer with enhanced clinical categorization"""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = Path(json_file_path)
        if not self.json_file_path.exists():
            raise FileNotFoundError(f"JSON file not found: {json_file_path}")
        
        # Medical knowledge base for classification
        self.age_patterns = self._initialize_age_patterns()
        self.system_patterns = self._initialize_system_patterns()
        self.severity_patterns = self._initialize_severity_patterns()
        self.medication_patterns = self._initialize_medication_patterns()
    
    def _initialize_age_patterns(self) -> Dict[AgeGroup, List[str]]:
        """Initialize age group classification patterns"""
        return {
            AgeGroup.NEONATE: [
                r'newborn', r'neonate', r'birth', r'0-28 days', r'first month',
                r'umbilical', r'cord', r'neonatal'
            ],
            AgeGroup.INFANT_EARLY: [
                r'0-6 months', r'early infant', r'1-6 months', r'infant.*6.*month'
            ],
            AgeGroup.INFANT_LATE: [
                r'6-12 months', r'6.*11.*months', r'late infant'
            ],
            AgeGroup.TODDLER: [
                r'1-3 years', r'toddler', r'1.*2.*years', r'2.*3.*years'
            ],
            AgeGroup.PRESCHOOL: [
                r'3-5 years', r'preschool', r'3.*4.*years', r'4.*5.*years'
            ],
            AgeGroup.SCHOOL_AGE: [
                r'5-12 years', r'school.*age', r'child', r'pediatric'
            ],
            AgeGroup.ADOLESCENT: [
                r'adolescent', r'teenager', r'12-18 years', r'puberty'
            ],
            AgeGroup.YOUNG_ADULT: [
                r'young.*adult', r'18-35 years'
            ],
            AgeGroup.ADULT: [
                r'adult', r'35-65 years'
            ],
            AgeGroup.ELDERLY: [
                r'elderly', r'65.*years', r'geriatric', r'old.*age'
            ],
            AgeGroup.MATERNAL: [
                r'pregnancy', r'pregnant', r'maternal', r'obstetric', r'labor',
                r'delivery', r'postpartum', r'antenatal', r'prenatal'
            ]
        }
    
    def _initialize_system_patterns(self) -> Dict[MedicalSystem, List[str]]:
        """Initialize medical system classification patterns"""
        return {
            MedicalSystem.DERMATOLOGICAL: [
                r'skin', r'rash', r'itch', r'scabies', r'ringworm', r'boils',
                r'burns', r'wounds', r'ulcer', r'dermat'
            ],
            MedicalSystem.RESPIRATORY: [
                r'cough', r'breathing', r'chest', r'pneumonia', r'asthma',
                r'respiratory', r'lung', r'bronch'
            ],
            MedicalSystem.CARDIOVASCULAR: [
                r'heart', r'cardiac', r'blood pressure', r'hypertension',
                r'chest pain', r'cardiovascular'
            ],
            MedicalSystem.GASTROINTESTINAL: [
                r'abdominal', r'stomach', r'diarrhea', r'vomiting', r'constipation',
                r'gastro', r'intestinal', r'bowel'
            ],
            MedicalSystem.NEUROLOGICAL: [
                r'headache', r'seizure', r'convulsion', r'neurological',
                r'brain', r'head.*injury', r'mental.*health'
            ],
            MedicalSystem.GENITOURINARY: [
                r'urinary', r'kidney', r'bladder', r'genital', r'urine'
            ],
            MedicalSystem.OPHTHALMOLOGICAL: [
                r'eye', r'vision', r'sight', r'ophthalm', r'conjunctiv'
            ],
            MedicalSystem.ENT: [
                r'ear', r'nose', r'throat', r'hearing', r'ENT'
            ],
            MedicalSystem.OBSTETRIC: [
                r'pregnancy', r'labor', r'delivery', r'maternal', r'obstetric'
            ],
            MedicalSystem.EMERGENCY: [
                r'emergency', r'trauma', r'shock', r'unconscious', r'critical'
            ]
        }
    
    def _initialize_severity_patterns(self) -> Dict[Severity, List[str]]:
        """Initialize severity classification patterns"""
        return {
            Severity.MILD: [r'mild.*condition', r'minor'],
            Severity.MODERATE: [r'moderate.*condition'],
            Severity.SEVERE: [r'severe.*condition', r'serious'],
            Severity.CRITICAL: [r'critical', r'emergency', r'refer.*immediately', r'life.*threatening']
        }
    
    def _initialize_medication_patterns(self) -> Dict[str, str]:
        """Initialize medication parsing patterns"""
        return {
            'paracetamol': r'paracetamol.*?(\d+(?:\.\d+)?)\s*(?:mg|tab)',
            'amoxicillin': r'amoxicillin.*?(\d+(?:\.\d+)?)\s*(?:mg|tab)',
            'cotrimoxazole': r'cotrimoxazole.*?(\d+(?:\.\d+)?)\s*(?:mg|tab)',
        }
    
    def classify_age_group(self, text: str) -> AgeGroup:
        """Classify age group based on text content"""
        text_lower = text.lower()
        
        # Check for specific age mentions first
        age_matches = re.findall(r'(\d+)\s*(?:months?|years?)', text_lower)
        if age_matches:
            age_value = int(age_matches[0])
            if 'month' in text_lower:
                if age_value <= 1:
                    return AgeGroup.NEONATE
                elif age_value <= 6:
                    return AgeGroup.INFANT_EARLY
                elif age_value <= 12:
                    return AgeGroup.INFANT_LATE
            elif 'year' in text_lower:
                if age_value <= 3:
                    return AgeGroup.TODDLER
                elif age_value <= 5:
                    return AgeGroup.PRESCHOOL
                elif age_value <= 12:
                    return AgeGroup.SCHOOL_AGE
                elif age_value <= 18:
                    return AgeGroup.ADOLESCENT
                elif age_value <= 35:
                    return AgeGroup.YOUNG_ADULT
                elif age_value <= 65:
                    return AgeGroup.ADULT
                else:
                    return AgeGroup.ELDERLY
        
        # Pattern-based classification
        for age_group, patterns in self.age_patterns.items():
            if any(re.search(pattern, text_lower) for pattern in patterns):
                return age_group
        
        return AgeGroup.ALL_AGES
    
    def classify_medical_system(self, text: str) -> MedicalSystem:
        """Classify medical system based on text content"""
        text_lower = text.lower()
        
        for system, patterns in self.system_patterns.items():
            if any(re.search(pattern, text_lower) for pattern in patterns):
                return system
        
        return MedicalSystem.INFECTIOUS  # Default for unclassified
    
    def classify_severity(self, text: str) -> Severity:
        """Classify severity based on text content"""
        text_lower = text.lower()
        
        for severity, patterns in self.severity_patterns.items():
            if any(re.search(pattern, text_lower) for pattern in patterns):
                return severity
        
        return Severity.UNKNOWN
    
    def extract_medications(self, text: str) -> List[Dict[str, str]]:
        """Extract medication information from text"""
        medications = []
        text_lower = text.lower()
        
        # Common medication patterns
        med_patterns = {
            'Paracetamol': r'paracetamol.*?(\d+(?:\.\d+)?)\s*(?:mg|tab).*?(\w+)\s*(?:x|for)\s*(\d+)',
            'Amoxicillin': r'amoxicillin.*?(\d+(?:\.\d+)?)\s*(?:mg|tab).*?(\w+)\s*(?:x|for)\s*(\d+)',
            'Cotrimoxazole': r'cotrimoxazole.*?(\d+(?:\.\d+)?)\s*(?:mg|tab).*?(\w+)\s*(?:x|for)\s*(\d+)',
        }
        
        for med_name, pattern in med_patterns.items():
            matches = re.finditer(pattern, text_lower)
            for match in matches:
                medications.append({
                    'name': med_name,
                    'dose': f"{match.group(1)}mg",
                    'frequency': match.group(2),
                    'duration': f"{match.group(3)} days"
                })
        
        return medications
    
    def enhance_clinical_record(self, record: Dict[str, Any], record_id: str) -> EnhancedClinicalRecord:
        """Convert basic record to enhanced medical record"""
        full_text = f"{record.get('category', '')} {record.get('findings', '')} {record.get('clinical_judgement', '')} {record.get('actions', '')}"
        
        # Classifications
        age_group = self.classify_age_group(full_text)
        medical_system = self.classify_medical_system(full_text)
        severity = self.classify_severity(full_text)
        
        # Extract medications
        medications = self.extract_medications(record.get('actions', ''))
        
        # Parse differential diagnoses
        differential_dx = self._extract_differential_diagnoses(record.get('clinical_judgement', ''))
        
        # Extract immediate actions
        immediate_actions = self._extract_immediate_actions(record.get('actions', ''))
        
        # Extract referral criteria
        referral_criteria = self._extract_referral_criteria(record.get('actions', ''))
        
        return EnhancedClinicalRecord(
            record_id=record_id,
            category=record.get('category', '').strip(),
            age_group=age_group,
            medical_system=medical_system,
            severity=severity,
            chief_complaint=self._extract_chief_complaint(record.get('category', '')),
            clinical_findings=record.get('findings', '').strip(),
            differential_diagnosis=differential_dx,
            primary_diagnosis=self._extract_primary_diagnosis(record.get('clinical_judgement', '')),
            clinical_assessment=record.get('clinical_judgement', '').strip(),
            immediate_actions=immediate_actions,
            medications=medications,
            procedures=self._extract_procedures(record.get('actions', '')),
            referral_criteria=referral_criteria,
            follow_up_schedule=self._extract_follow_up(record.get('actions', '')),
            health_education=self._extract_health_education(record.get('health_education', '')),
            prevention_measures=self._extract_prevention_measures(record.get('health_education', '')),
            contraindications=self._extract_contraindications(record.get('actions', '')),
            complications=self._extract_complications(full_text),
            prognosis=self._determine_prognosis(severity, medical_system),
            evidence_level="Clinical Guidelines",
            last_updated="2025-08-06"
        )
    
    def _extract_chief_complaint(self, category: str) -> str:
        """Extract chief complaint from category"""
        # Clean up category text
        complaint = re.sub(r'^\d+\.\d*\s*', '', category)  # Remove numbering
        complaint = re.sub(r'\n.*', '', complaint)  # Take first line only
        return complaint.strip()
    
    def _extract_differential_diagnoses(self, judgement: str) -> List[str]:
        """Extract differential diagnoses from clinical judgement"""
        diagnoses = []
        suspect_pattern = r'suspect\s+([^.\n]+)'
        matches = re.finditer(suspect_pattern, judgement.lower())
        for match in matches:
            diagnoses.append(match.group(1).strip().title())
        return diagnoses
    
    def _extract_primary_diagnosis(self, judgement: str) -> str:
        """Extract primary diagnosis from clinical judgement"""
        suspect_match = re.search(r'suspect\s+([^.\n]+)', judgement.lower())
        if suspect_match:
            return suspect_match.group(1).strip().title()
        return ""
    
    def _extract_immediate_actions(self, actions: str) -> List[str]:
        """Extract immediate actions from action text"""
        action_list = []
        # Split by numbered items
        items = re.split(r'\d+\.', actions)
        for item in items[1:]:  # Skip first empty item
            if item.strip():
                action_list.append(item.strip())
        return action_list
    
    def _extract_referral_criteria(self, actions: str) -> List[str]:
        """Extract referral criteria from actions"""
        criteria = []
        refer_patterns = [
            r'refer.*if\s+([^.\n]+)',
            r'refer.*when\s+([^.\n]+)',
            r'refer.*immediately',
            r'refer.*urgently'
        ]
        for pattern in refer_patterns:
            matches = re.finditer(pattern, actions.lower())
            for match in matches:
                if match.groups():
                    criteria.append(match.group(1).strip())
                else:
                    criteria.append("Immediate referral required")
        return criteria
    
    def _extract_procedures(self, actions: str) -> List[str]:
        """Extract procedures from actions"""
        procedures = []
        procedure_patterns = [
            r'apply\s+([^.\n]+)',
            r'clean\s+([^.\n]+)',
            r'dress\s+([^.\n]+)',
            r'immobilize\s+([^.\n]+)'
        ]
        for pattern in procedure_patterns:
            matches = re.finditer(pattern, actions.lower())
            for match in matches:
                procedures.append(match.group(1).strip())
        return procedures
    
    def _extract_follow_up(self, actions: str) -> str:
        """Extract follow-up schedule from actions"""
        follow_up_patterns = [
            r'review.*?(\d+\s*(?:days?|weeks?|months?))',
            r'follow.*?up.*?(\d+\s*(?:days?|weeks?|months?))',
            r'return.*?(\d+\s*(?:days?|weeks?|months?))'
        ]
        for pattern in follow_up_patterns:
            match = re.search(pattern, actions.lower())
            if match:
                return f"Review in {match.group(1)}"
        return ""
    
    def _extract_health_education(self, education: str) -> List[str]:
        """Extract health education points"""
        if not education:
            return []
        
        education_points = []
        # Split by bullet points or numbers
        items = re.split(r'[•\*\-]|\d+\.', education)
        for item in items:
            if item.strip() and len(item.strip()) > 10:
                education_points.append(item.strip())
        return education_points
    
    def _extract_prevention_measures(self, education: str) -> List[str]:
        """Extract prevention measures from health education"""
        prevention = []
        prevention_patterns = [
            r'prevent\w*\s+([^.\n]+)',
            r'avoid\s+([^.\n]+)',
            r'hygiene\s+([^.\n]+)'
        ]
        for pattern in prevention_patterns:
            matches = re.finditer(pattern, education.lower())
            for match in matches:
                prevention.append(match.group(1).strip())
        return prevention
    
    def _extract_contraindications(self, actions: str) -> List[str]:
        """Extract contraindications from actions"""
        contraindications = []
        contra_patterns = [
            r'do not\s+([^.\n]+)',
            r'avoid\s+([^.\n]+)',
            r'contraindicated\s+([^.\n]+)'
        ]
        for pattern in contra_patterns:
            matches = re.finditer(pattern, actions.lower())
            for match in matches:
                contraindications.append(match.group(1).strip())
        return contraindications
    
    def _extract_complications(self, text: str) -> List[str]:
        """Extract potential complications"""
        complications = []
        if 'severe' in text.lower():
            complications.append("Potential for severe complications")
        if 'refer' in text.lower():
            complications.append("May require specialist care")
        return complications
    
    def _determine_prognosis(self, severity: Severity, system: MedicalSystem) -> str:
        """Determine prognosis based on severity and system"""
        if severity == Severity.MILD:
            return "Good prognosis with appropriate treatment"
        elif severity == Severity.MODERATE:
            return "Good prognosis with timely intervention"
        elif severity == Severity.SEVERE:
            return "Guarded prognosis, requires immediate care"
        elif severity == Severity.CRITICAL:
            return "Critical condition, immediate intervention required"
        else:
            return "Prognosis depends on timely diagnosis and treatment"
    
    def analyze_and_enhance(self) -> Dict[str, List[Dict[str, Any]]]:
        """Main method to analyze and enhance all clinical records"""
        logger.info(f"Starting medical enhancement of {self.json_file_path}")
        
        # Load existing records
        with open(self.json_file_path, 'r', encoding='utf-8') as f:
            raw_records = json.load(f)
        
        # Group enhanced records by age group
        enhanced_records_by_age = {}
        
        for i, record in enumerate(raw_records, 1):
            enhanced_record = self.enhance_clinical_record(record, f"REC_{i:04d}")
            
            age_group_key = enhanced_record.age_group.value
            if age_group_key not in enhanced_records_by_age:
                enhanced_records_by_age[age_group_key] = []
            
            enhanced_records_by_age[age_group_key].append(enhanced_record.to_dict())
            
            if i % 100 == 0:
                logger.info(f"Processed {i} records")
        
        logger.info(f"Enhanced {len(raw_records)} records into {len(enhanced_records_by_age)} age groups")
        return enhanced_records_by_age

def main():
    """Main function to run the medical enhanced analyzer"""
    json_file = "enhanced_clinical_analysis_results.json"
    
    try:
        analyzer = MedicalEnhancedAnalyzer(json_file)
        enhanced_results = analyzer.analyze_and_enhance()
        
        # Save enhanced results
        output_file = "medical_enhanced_clinical_records.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_results, f, indent=2, ensure_ascii=False)
        
        print(f"\nMedical enhancement complete! Results saved to {output_file}")
        
        # Display summary by age group
        print("\nSummary by Age Group:")
        for age_group, records in enhanced_results.items():
            print(f"{age_group}: {len(records)} records")
        
        print(f"\nTotal enhanced records: {sum(len(records) for records in enhanced_results.values())}")
    
    except Exception as e:
        logger.error(f"Error during medical enhancement: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
